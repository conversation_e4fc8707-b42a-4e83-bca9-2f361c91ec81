const { CONFIG } = require("../config");
const { getMySqlPromiseConnection } = require("../config/mysql.db")

const DELETION_ORDER = [
    'order_items',
    'order_discounts',
    'partial_payments',
    'invoice_sequences',
    'invoices',
    'orders'
];
const ALLOWED_TABLES = [
    'order_items',
    'order_discounts',
    'partial_payments',
    'invoice_sequences',
    'invoices',
    'orders'
];

exports.getFeedBacksDB = async (tenantId) => {
    const conn = await getMySqlPromiseConnection();
    try {
      const sql = `
        SELECT
          id,
          feedback_type,
          message,
          tenant_id,
          created_at
        FROM
          feedbacks
        WHERE
          tenant_id = ?;
      `;
      const [result] = await conn.query(sql, [tenantId]);
      return result; // Tüm geri bildirimleri döndür
    } catch (error) {
      console.error("Feedbacks sorgusunda hata:", error);
      throw error;
    } finally {
      conn.release();
    }
  };


  exports.getTenantConfigDB = async (tenantId) => {
    const conn = await getMySqlPromiseConnection();
    try {
        const sql = `
            SELECT
                background_color,
                text_color,
                primary_color,
                header_color,
                header_text_color,
                menu_button_background_color,
                menu_button_text_color,
                complaint_button_background_color,
                complaint_button_text_color,
                suggestion_button_background_color,
                suggestion_button_text_color,
                thank_you_button_background_color,
                thank_you_button_text_color,
                footer_background_color,
                footer_text_color
            FROM tenant_config
            WHERE tenant_id = ?
            LIMIT 1;
        `;
        const [result] = await conn.query(sql, [tenantId]);
        return result[0]; // İlk sonucu döndür
    } catch (error) {
        console.error("Tenant config sorgusunda hata:", error);
        throw error;
    } finally {
        conn.release();
    }
};

exports.deleteTenantDataDB = async (tenantId, tables) => {
    const conn = await getMySqlPromiseConnection();
    try {
      await conn.beginTransaction();

      const toDelete = DELETION_ORDER.filter(tbl => tables.includes(tbl));

      if (toDelete.length === 0) {
        throw new Error('No valid tables selected');
      }

      for (const table of toDelete) {
        if (!ALLOWED_TABLES.includes(table)) {
          throw new Error(`Invalid table: ${table}`);
        }
        await conn.query(`DELETE FROM \`${table}\` WHERE tenant_id = ?`, [tenantId]);
      }

      await conn.commit();
      return toDelete;
    } catch(err) {
      await conn.rollback();
      throw err;
    } finally {
      conn.release();
    }
  };

const insertTenantConfigIfNotExists = async (tenantId) => {
    const conn = await getMySqlPromiseConnection();
    try {
        const sql = `
            INSERT INTO tenant_config (tenant_id)
            SELECT ?
            WHERE NOT EXISTS (
                SELECT 1 FROM tenant_config WHERE tenant_id = ?
            );
        `;
        await conn.query(sql, [tenantId, tenantId]);
    } catch (error) {
        console.error("Error in insertTenantConfigIfNotExists:", error);
        throw error;
    } finally {
        conn.release();
    }
};


exports.updateTenantConfigDB = async (tenantId, configData) => {
    const conn = await getMySqlPromiseConnection();

    try {
        // İlk kayıt yoksa ekle
        await insertTenantConfigIfNotExists(tenantId);

        // Güncelleme sorgusu
        const sql = `
            UPDATE tenant_config
            SET
                header_color = ?,
                background_color = ?,
                header_text_color = ?,
                menu_button_background_color = ?,
                menu_button_text_color = ?,
                complaint_button_background_color = ?,
                complaint_button_text_color = ?,
                suggestion_button_background_color = ?,
                suggestion_button_text_color = ?,
                thank_you_button_background_color = ?,
                thank_you_button_text_color = ?,
                footer_background_color = ?,
                footer_text_color = ?,
                text_color = ?,
                price_text_color = ?,
                head_text_color = ?
            WHERE tenant_id = ?;
        `;
        const params = [
            configData.header_color,
            configData.background_color,
            configData.header_text_color,
            configData.menu_button_background_color,
            configData.menu_button_text_color,
            configData.complaint_button_background_color,
            configData.complaint_button_text_color,
            configData.suggestion_button_background_color,
            configData.suggestion_button_text_color,
            configData.thank_you_button_background_color,
            configData.thank_you_button_text_color,
            configData.footer_background_color,
            configData.footer_text_color,
            configData.text_color,
            configData.price_text_color,
            configData.head_text_color,
            tenantId,
        ];

        await conn.query(sql, params);
        return { success: true, message: "Config başarıyla güncellendi." };
    } catch (error) {
        console.error("Tenant config güncellenirken hata:", error);
        throw error;
    } finally {
        conn.release();
    }
};

exports.getTenantIdFromQRCode = async (qrcode) => {
    const conn = await getMySqlPromiseConnection();
    try {

        const sql = `
        SELECT
            tenant_id
        FROM
            store_details
        WHERE
            unique_qr_code = ?
        LIMIT 1;
        `;

        const [result] = await conn.query(sql, [qrcode]);
        return result[0]?.tenant_id;
    } catch (error) {
        console.error(error);
        throw error;
    } finally {
        conn.release();
    }
}

exports.getCurrencyDB = async (tenantId) => {
    const conn = await getMySqlPromiseConnection();
    try {

        const sql = `
        SELECT
            currency
        FROM
            store_details
        WHERE tenant_id = ?
        LIMIT 1;
        `;

        const [result] = await conn.query(sql, [tenantId]);
        return result[0]?.currency;
    } catch (error) {
        console.error(error);
        throw error;
    } finally {
        conn.release();
    }
};

exports.getStoreSettingDB = async (tenantId) => {
    const conn = await getMySqlPromiseConnection();

    try {
        const sql = `
        SELECT
            sd.tenant_id,
            sd.store_name,
            sd.store_image,
            sd.address,
            sd.phone,
            sd.email,
            sd.currency,
            sd.image,
            sd.slides,
            sd.default_order_type,
            sd.dine_in_enabled,
            sd.delivery_enabled,
            sd.takeaway_enabled,
            sd.is_qr_menu_enabled,
            sd.unique_qr_code,
            sd.is_qr_order_enabled,
            sd.autolock,
            sd.mali_mode_active,
            sd.cash_register_enabled,
            sd.facebook,
            sd.instagram,
            sd.twitter,
            sd.whatsapp,
            tc.background_color,
            tc.text_color,
            tc.primary_color,
            tc.header_color,
            tc.header_text_color,
            tc.menu_button_background_color,
            tc.menu_button_text_color,
            tc.complaint_button_background_color,
            tc.complaint_button_text_color,
            tc.suggestion_button_background_color,
            tc.suggestion_button_text_color,
            tc.thank_you_button_background_color,
            tc.thank_you_button_text_color,
            tc.footer_background_color,
            tc.footer_text_color,
            tc.price_text_color,
            tc.text_color,
            tc.head_text_color
        FROM store_details sd
        LEFT JOIN tenant_config tc ON sd.tenant_id = tc.tenant_id
        WHERE sd.tenant_id = ?
        LIMIT 1;
        `;

        const [result] = await conn.query(sql, [tenantId]);
        return result[0]; // İlk sonucu döndür
    } catch (error) {
        console.error("Store settings sorgusunda hata:", error);
        throw error;
    } finally {
        conn.release();
    }
};


exports.setStoreSettingDB = async (storeName, address, phone, email, currency, defaultOrderType, dineInEnabled, deliveryEnabled, takeawayEnabled,  isQRMenuEnabled, isQROrderEnabled , facebook, instagram, twitter, whatsapp, uniqueQRCode, tenantId) => {
    const conn = await getMySqlPromiseConnection();

    try {
        const sql = `
        INSERT INTO store_details ( store_name, address, phone, email, currency, default_order_type, dine_in_enabled, delivery_enabled, takeaway_enabled, is_qr_menu_enabled, is_qr_order_enabled, facebook, instagram, twitter, whatsapp, unique_qr_code, tenant_id)
        VALUES
        (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        ON DUPLICATE KEY UPDATE
        store_name = VALUES(store_name),
        is_qr_menu_enabled = VALUES(is_qr_menu_enabled),
        address = VALUES(address),
        phone = VALUES(phone),
        email = VALUES(email),
        currency = VALUES(currency),
        tenant_id = VALUES(tenant_id),
        default_order_type = VALUES(default_order_type),
        dine_in_enabled = VALUES(dine_in_enabled),
        delivery_enabled = VALUES(delivery_enabled),
        takeaway_enabled = VALUES(takeaway_enabled),
        is_qr_order_enabled = VALUES(is_qr_order_enabled),
        facebook = VALUES(facebook),
        instagram = VALUES(instagram),
        twitter = VALUES(twitter),
        whatsapp = VALUES(whatsapp);
        `;

        await conn.query(sql, [storeName, address, phone, email, currency, defaultOrderType, dineInEnabled, deliveryEnabled, takeawayEnabled, isQRMenuEnabled,isQROrderEnabled, facebook, instagram, twitter, whatsapp, uniqueQRCode, tenantId]);
        return;
    } catch (error) {
        console.error(error);
        throw error;
    } finally {
        conn.release();
    }
};

exports.uploadStoreImageDB = async (image, uniqueId, tenantId) => {
    const conn = await getMySqlPromiseConnection();
    try {
        const sql = `
        INSERT INTO store_details (tenant_id, store_image, unique_id)
        VALUES (?, ?, ?)
        ON DUPLICATE KEY UPDATE
        tenant_id = VALUES(tenant_id),
        store_image = VALUES(store_image),
        unique_id = VALUES(unique_id);`

        await conn.query(sql, [tenantId, image, uniqueId]);
        return;
    } catch (error) {
        console.error(error);
        throw error;
    } finally {
        conn.release();
    }
};

exports.deleteStoreImageDB = async (image, uniqueId, tenantId) => {
    const conn = await getMySqlPromiseConnection();
    try {
        const sql = `
        UPDATE store_details SET
        store_image = ?
        WHERE unique_id = ? AND tenant_id = ?;`

        await conn.query(sql, [image, uniqueId, tenantId]);
        return;
    } catch (error) {
        console.error(error);
        throw error;
    } finally {
        conn.release();
    }
};

exports.updateSlidersItemImageDB = async (newImage, tenantId) => {
    const conn = await getMySqlPromiseConnection();
    try {
        // Önce mevcut slides'ı alalım
        const [rows] = await conn.query(
            'SELECT slides FROM store_details WHERE tenant_id = ?',
            [tenantId]
        );

        let existingSlides = [];
        if (rows.length > 0 && rows[0].slides) {
            existingSlides = rows[0].slides.split(',').filter(Boolean);
        }

        // Yeni resmi ekleyelim
        if (newImage) {
            existingSlides.push(newImage);
        }

        // Güncellenmiş listeyi string olarak birleştirelim
        const updatedSlides = existingSlides.join(',');

        const sql = `
        UPDATE store_details SET
        slides = ?
        WHERE tenant_id = ?;
        `;

        await conn.query(sql, [updatedSlides, tenantId]);
        return;
    } catch (error) {
        console.error(error);
        throw error;
    } finally {
        conn.release();
    }
}

exports.removeSlidersItemImageDB = async (imageToRemove, tenantId) => {
    const conn = await getMySqlPromiseConnection();
    try {
        // Mevcut slides'ı alalım
        const [rows] = await conn.query(
            'SELECT slides FROM store_details WHERE tenant_id = ?',
            [tenantId]
        );

        let existingSlides = [];
        if (rows.length > 0 && rows[0].slides) {
            existingSlides = rows[0].slides.split(',').filter(Boolean);
        }

        // Silinecek resmi çıkaralım
        existingSlides = existingSlides.filter(slide => slide !== imageToRemove);

        // Güncellenmiş listeyi string olarak birleştirelim
        const updatedSlides = existingSlides.join(',');

        const sql = `
        UPDATE store_details SET
        slides = ?
        WHERE tenant_id = ?;
        `;

        await conn.query(sql, [updatedSlides, tenantId]);
        return;
    } catch (error) {
        console.error(error);
        throw error;
    } finally {
        conn.release();
    }
}

exports.getQRMenuCodeDB = async (tenantId) => {
    const conn = await getMySqlPromiseConnection();

    try {
        const sql = `
        SELECT unique_qr_code FROM store_details
        WHERE tenant_id = ?
        LIMIT 1;
        `;

        const [result] = await conn.query(sql, [tenantId]);
        return result[0]?.unique_qr_code || null;
    } catch (error) {
        console.error(error);
        throw error;
    } finally {
        conn.release();
    }
};

exports.updateQRMenuCodeDB = async (uniqueQRCode, tenantId) => {
    const conn = await getMySqlPromiseConnection();

    try {
        const sql = `
        UPDATE store_details SET unique_qr_code = ?
        WHERE tenant_id = ?;
        `;

        await conn.query(sql, [uniqueQRCode, tenantId]);
        return;
    } catch (error) {
        console.error(error);
        throw error;
    } finally {
        conn.release();
    }
};

exports.getPrintSettingDB = async (tenantId) => {
    const conn = await getMySqlPromiseConnection();

    try {
        const sql = `
        SELECT  page_format, header, footer, show_notes, is_enable_print, show_store_details, show_customer_details, print_token FROM print_settings
        WHERE tenant_id = ?
        LIMIT 1;
        `;

        const [result] = await conn.query(sql, [tenantId]);
        return result[0];
    } catch (error) {
        console.error(error);
        throw error;
    } finally {
        conn.release();
    }
};

exports.setPrintSettingDB = async (pageFormat, header, footer, showNotes, isEnablePrint, showStoreDetails, showCustomerDetails, printToken, tenantId) => {
    const conn = await getMySqlPromiseConnection();

    try {
        const sql = `
        INSERT INTO print_settings
        ( page_format, header, footer, show_notes, is_enable_print, show_store_details, show_customer_details, print_token, tenant_id)
        VALUES
        ( ?, ?, ?, ?, ?, ?, ?, ?, ?)
        ON DUPLICATE KEY UPDATE
        page_format = VALUES(page_format),
        header = VALUES(header),
        footer = VALUES(footer),
        show_notes = VALUES(show_notes),
        is_enable_print = VALUES(is_enable_print),
        show_store_details = VALUES(show_store_details),
        show_customer_details = VALUES(show_customer_details),
        print_token = VALUES(print_token),
        tenant_id = VALUES(tenant_id);
        `;

        await conn.query(sql, [pageFormat, header, footer, showNotes, isEnablePrint, showStoreDetails, showCustomerDetails, printToken, tenantId]);
        return;
    } catch (error) {
        console.error(error);
        throw error;
    } finally {
        conn.release();
    }
};

exports.addTaxDB = async (title, rate, type, tenantId) => {
    const conn = await getMySqlPromiseConnection();

    try {
        const sql = `
        INSERT INTO taxes
        (title, rate, type, tenant_id)
        VALUES (?, ?, ?, ?);
        `;

        const [result] = await conn.query(sql, [title, rate, type, tenantId]);
        return result.insertId;
    } catch (error) {
        console.error(error);
        throw error;
    } finally {
        conn.release();
    }
};

exports.getTaxesDB = async (tenantId) => {
    const conn = await getMySqlPromiseConnection();

    try {
        const sql = `
        SELECT id, title, rate, type FROM taxes WHERE tenant_id = ?;
        `;

        const [result] = await conn.query(sql, [tenantId]);
        return result;
    } catch (error) {
        console.error(error);
        throw error;
    } finally {
        conn.release();
    }
};

exports.getTaxDB = async (taxId, tenantId) => {
    const conn = await getMySqlPromiseConnection();

    try {
        const sql = `
        SELECT id, title, rate, type FROM taxes
        WHERE id = ? AND tenant_id = ?
        LIMIT 1;
        `;

        const [result] = await conn.query(sql, [taxId, tenantId]);
        return result[0];
    } catch (error) {
        console.error(error);
        throw error;
    } finally {
        conn.release();
    }
};

exports.deleteTaxDB = async (id, tenantId) => {
    const conn = await getMySqlPromiseConnection();

    try {
        const sql = `
        DELETE FROM taxes WHERE id = ? AND tenant_id = ?;
        `;

        await conn.query(sql, [id, tenantId]);
        return;
    } catch (error) {
        console.error(error);
        throw error;
    } finally {
        conn.release();
    }
};

exports.updateTaxDB = async (id, title, rate, type, tenantId) => {
    const conn = await getMySqlPromiseConnection();

    try {
        const sql = `
        UPDATE taxes
        SET
        title = ?, rate = ?, type = ?
        WHERE id = ? AND tenant_id = ?
        `;

        await conn.query(sql, [title, rate, type, id, tenantId]);
        return;
    } catch (error) {
        console.error(error);
        throw error;
    } finally {
        conn.release();
    }
};


exports.addPaymentTypeDB = async (title, isActive, tenantId, icon, hideOnPos = 0, isCash = 0, isMali = 0) => {
    const conn = await getMySqlPromiseConnection();

    try {
        const sql = `
        INSERT INTO payment_types
        (title, is_active, hide_on_pos, tenant_id, icon, isCash, is_mali)
        VALUES (?, ?, ?, ?, ?, ?, ?);
        `;

        const [result] = await conn.query(sql, [title, isActive, hideOnPos, tenantId, icon, isCash, isMali]);
        return result.insertId;
    } catch (error) {
        console.error(error);
        throw error;
    } finally {
        conn.release();
    }
};

exports.getPaymentTypesDB = async (activeOnly=false, tenantId, forPOS=false) => {
    const conn = await getMySqlPromiseConnection();

    try {
        let sql = `
        SELECT id, title, is_active, hide_on_pos, icon, isCash, is_mali FROM payment_types
        WHERE tenant_id = ?;
        `;

        if(activeOnly && forPOS) {
            // POS için: aktif olanlar + hide_on_pos = 0 olanlar
            sql = `
            SELECT id, title, is_active, hide_on_pos, icon, isCash, is_mali FROM payment_types
            WHERE is_active = 1 AND hide_on_pos = 0 AND tenant_id = ?;
            `;
        } else if(activeOnly) {
            sql = `
            SELECT id, title, is_active, hide_on_pos, icon, isCash, is_mali FROM payment_types
            WHERE is_active = 1 AND tenant_id = ?;
            `;
        } else if(forPOS) {
            // POS için: sadece hide_on_pos = 0 olanlar (mali mode otomatik olarak etkili olur)
            sql = `
            SELECT id, title, is_active, hide_on_pos, icon, isCash, is_mali FROM payment_types
            WHERE hide_on_pos = 0 AND tenant_id = ?;
            `;
        }

        const [result] = await conn.query(sql, [tenantId]);
        return result;
    } catch (error) {
        console.error(error);
        throw error;
    } finally {
        conn.release();
    }
};

exports.updatePaymentTypeDB = async (id, title, isActive, tenantId, icon, hideOnPos, isCash, isMali) => {
    const conn = await getMySqlPromiseConnection();

    try {
        const sql = `
        UPDATE payment_types
        SET title = ?, is_active = ?, icon = ?, hide_on_pos = ?, isCash = ?, is_mali = ?
        WHERE id = ? AND tenant_id = ?;
        `;

        const [result] = await conn.query(sql, [title, isActive, icon, hideOnPos, isCash, isMali, id, tenantId]);
        return;
    } catch (error) {
        console.error(error);
        throw error;
    } finally {
        conn.release();
    }
};

exports.togglePaymentTypeDB = async (id, isActive, tenantId) => {
    const conn = await getMySqlPromiseConnection();

    try {
        const sql = `
        UPDATE payment_types
        SET is_active = ?
        WHERE id = ? AND tenant_id = ?;
        `;

        const [result] = await conn.query(sql, [isActive, id, tenantId]);
        return;
    } catch (error) {
        console.error(error);
        throw error;
    } finally {
        conn.release();
    }
};

exports.deletePaymentTypeDB = async (id, tenantId) => {
    const conn = await getMySqlPromiseConnection();

    try {
        const sql = `
        DELETE FROM payment_types
        WHERE id = ? AND tenant_id = ?;
        `;

        await conn.query(sql, [id, tenantId]);
        return;
    } catch (error) {
        console.error(error);
        throw error;
    } finally {
        conn.release();
    }
};

exports.addStoreTableDB = async (title, floor, seatingCapacity, tenantId) => {
    const conn = await getMySqlPromiseConnection();

    try {
        const sql = `
        INSERT INTO store_tables
        (table_title, floor, seating_capacity, tenant_id)
        VALUES (?, ?, ?, ?);
        `;

        const [result] = await conn.query(sql, [title, floor, seatingCapacity, tenantId]);
        return result.insertId;
    } catch (error) {
        console.error(error);
        throw error;
    } finally {
        conn.release();
    }
};

/**
 * Toplu masa ekler
 * @param {string} baseTitle - Temel masa başlığı
 * @param {number} count - Eklenecek masa sayısı
 * @param {number} floor - Kat ID
 * @param {number} seatingCapacity - Oturma kapasitesi
 * @param {number} tenantId - Kiracı ID
 * @returns {Array} Eklenen masaların ID'leri
 */
exports.bulkAddStoreTablesDB = async (baseTitle, count, floor, seatingCapacity, tenantId) => {
    const conn = await getMySqlPromiseConnection();

    try {
        await conn.beginTransaction();

        const tableIds = [];

        for (let i = 1; i <= count; i++) {
            const tableTitle = `${baseTitle} - ${i}`;

            const [result] = await conn.query(
                `INSERT INTO store_tables (table_title, floor, seating_capacity, tenant_id)
                 VALUES (?, ?, ?, ?)`,
                [tableTitle, floor, seatingCapacity, tenantId]
            );

            tableIds.push(result.insertId);
        }

        await conn.commit();
        return tableIds;
    } catch (error) {
        await conn.rollback();
        console.error("Toplu masa eklenirken hata oluştu:", error);
        throw error;
    } finally {
        conn.release();
    }
};

exports.getFloorsByTenantId = async (tenantId) => {
    const conn = await getMySqlPromiseConnection();

    try {
        const sql = `
        SELECT
            id AS floor_id,
            title AS floor_name,
            description AS floor_description,
            is_active,
            tenant_id
        FROM
            floor
        WHERE
            tenant_id = ?;
        `;

        const [result] = await conn.query(sql, [tenantId]);
        return result;
    } catch (error) {
        console.error("Floors getirme hatası:", error);
        throw error;
    } finally {
        conn.release();
    }
};

exports.getStoreTablesDB = async (tenantId) => {
    const conn = await getMySqlPromiseConnection();

    try {
        const sql = `
        SELECT
        id,
        HEX(AES_ENCRYPT(HEX(id), ?)) AS encrypted_id,
        table_title,
        table_status,
        floor,
        seating_capacity
        FROM store_tables
        WHERE tenant_id = ?;
        `;

        const [result] = await conn.query(sql, [CONFIG.ENCRYPTION_KEY, tenantId]);
        return result;
    } catch (error) {
        console.error(error);
        throw error;
    } finally {
        conn.release();
    }
};

exports.getStoreTableByEncryptedIdDB = async (tenantId, encryptedTableId) => {
    const conn = await getMySqlPromiseConnection();

    try {
        const sql = `
        SELECT
        id,
        table_title,
        floor,
        seating_capacity
        FROM store_tables
        WHERE tenant_id = ? AND AES_DECRYPT(UNHEX(?), ?) = HEX(id)
        LIMIT 1;
        `;

        const [result] = await conn.query(sql, [tenantId, encryptedTableId, CONFIG.ENCRYPTION_KEY]);

        return result[0] || null;
    } catch (error) {
        console.error(error);
        throw error;
    } finally {
        conn.release();
    }
};

exports.updateStoreTableDB = async (id, title, floor, seatingCapacity, tenantId) => {
    const conn = await getMySqlPromiseConnection();

    try {
        const sql = `
        UPDATE store_tables SET
        table_title = ?, floor = ?, seating_capacity = ?
        WHERE id = ? AND tenant_id = ?;
        `;

        await conn.query(sql, [title, floor, seatingCapacity, id, tenantId]);
        return;
    } catch (error) {
        console.error(error);
        throw error;
    } finally {
        conn.release();
    }
};

exports.deleteStoreTableDB = async (id, tenantId) => {
    const conn = await getMySqlPromiseConnection();

    try {
        const sql = `
        DELETE FROM store_tables
        WHERE id = ? AND tenant_id = ?;
        `;

        await conn.query(sql, [id, tenantId]);
        return;
    } catch (error) {
        console.error(error);
        throw error;
    } finally {
        conn.release();
    }
};

exports.addCategoryDB = async (title, tenantId, parent_id) => {
    const conn = await getMySqlPromiseConnection();
    try {
      const sql = `
        INSERT INTO categories
        (title, tenant_id, parent_id)
        VALUES (?, ?, ?);
      `;
      // parent_id yoksa NULL gönderiyoruz
      const [result] = await conn.query(sql, [title, tenantId, parent_id || null]);
      return result.insertId;
    } catch (error) {
      console.error(error);
      throw error;
    } finally {
      conn.release();
    }
  };


  exports.getCategoriesDB = async (tenantId) => {
    const conn = await getMySqlPromiseConnection();
    try {
      const sql = `
        SELECT
          c.id,
          c.title,
          c.cat_image,
          c.sort_order,
          c.printer_id,
          c.parent_id,
          p.name AS printer_name,
          pc.title AS parent_title
        FROM categories c
        LEFT JOIN printers p ON c.printer_id = p.id
        LEFT JOIN categories pc ON c.parent_id = pc.id
        WHERE c.tenant_id = ?
        ORDER BY c.sort_order ASC, c.id ASC;
      `;
      const [result] = await conn.query(sql, [tenantId]);
      return result.map(category => ({
        id: category.id,
        title: category.title,
        cat_image: category.cat_image,
        sort_order: category.sort_order,
        printer: category.printer_id ? { id: category.printer_id, name: category.printer_name } : null,
        parent_id: category.parent_id,
        parent_title: category.parent_title || null
      }));
    } catch (error) {
      console.error(error);
      throw error;
    } finally {
      conn.release();
    }
  };



  exports.updateCategoryDB = async (id, title, printerId, tenantId, parent_id) => {
    const conn = await getMySqlPromiseConnection();
    try {
      const sql = `
        UPDATE categories
        SET title = ?, printer_id = ?, parent_id = ?
        WHERE id = ? AND tenant_id = ?;
      `;
      const printerValue = (printerId !== undefined && printerId !== "") ? printerId : null;
      await conn.execute(sql, [title, printerValue, parent_id || null, id, tenantId]);
      return;
    } catch (error) {
      console.error("Kategori güncellenirken hata oluştu:", error);
      throw error;
    } finally {
      conn.release();
    }
  };


exports.updateCategoryImageDB = async (id, image, tenantId) => {
    const conn = await getMySqlPromiseConnection();
    try {

        const sql = `
        UPDATE categories SET
        cat_image = ?
        WHERE id = ? AND tenant_id = ?;
        `;

        await conn.query(sql, [image, id, tenantId]);

        return;
    } catch (error) {
        console.error(error);
        throw error;
    } finally {
        conn.release();
    }
}

exports.deleteCategoryDB = async (id, tenantId) => {
    const conn = await getMySqlPromiseConnection();

    try {
        const sql = `
        DELETE FROM categories
        WHERE id = ? AND tenant_id = ?;
        `;

        await conn.query(sql, [id, tenantId]);
        return;
    } catch (error) {
        console.error(error);
        throw error;
    } finally {
        conn.release();
    }
};


exports.placeOrderViaQrMenuDB = async (tenantId, deliveryType , cartItems, customerType, customerId, tableId, customerName ,paymentStatus = 'pending') => {
    const conn = await getMySqlPromiseConnection();

    try {
      // start transaction
      await conn.beginTransaction();

      // step 1: save data to orders table
      const [orderResult] = await conn.query(`INSERT INTO qr_orders (delivery_type, customer_type, customer_id, table_id, payment_status, tenant_id) VALUES (?, ?, ?, ?, ?, ?)`, [deliveryType, customerType, customerId, tableId, paymentStatus || 'pending', tenantId]);

      const orderId = orderResult.insertId;

      // step 2: save data to order_items
      const sqlOrderItems = `
      INSERT INTO qr_order_items
      (order_id, item_id, variant_id, price, quantity, notes, addons, tenant_id)
      VALUES ?
      `;

      await conn.query(sqlOrderItems, [cartItems.map((item)=>[orderId, item.id, item.variant_id, item.price, item.quantity, item.notes, item?.addons_ids?.length > 0 ? JSON.stringify(item.addons_ids):null, tenantId ])]);


			// Step 3 : Search customer by phone in customer table - if not existing - create one
			if(customerId){
				const sqlIsExistingCustomer = `
					SELECT 1 from customers where phone = ? AND tenant_id = ?
			`

				const [existingCustomer] = await conn.query(sqlIsExistingCustomer , [customerId, tenantId]);


				if (!existingCustomer.length) {
					const sqlAddCustomer = `
							INSERT INTO customers (phone, name,tenant_id) VALUES (?, ?,?)
					`;

					await conn.query(sqlAddCustomer, [customerId, customerName, tenantId]);
				}
			}


      // step 7: commit transaction / if any exception occurs then rollback
      await conn.commit();

      return {
        orderId
      }
    } catch (error) {
      console.error(error);
      await conn.rollback();
      throw error;
    } finally {
      conn.release();
    }
  };

// Mali Mode Fonksiyonları
exports.enableMaliModeDB = async (tenantId) => {
    const conn = await getMySqlPromiseConnection();

    try {
        // Mali mode'u aktifleştir
        await conn.query(`
            UPDATE store_details
            SET mali_mode_active = 1
            WHERE tenant_id = ?
        `, [tenantId]);

        // Payment types'ları güncelle - Tüm nakit türlerini gizle, sadece mali nakit göster
        await conn.query(`
            UPDATE payment_types
            SET hide_on_pos = CASE
                WHEN isCash = 1 AND is_mali = 0 THEN 1  -- Normal nakit türlerini gizle
                WHEN isCash = 1 AND is_mali = 1 THEN 0  -- Mali nakit türlerini göster
                ELSE hide_on_pos  -- Diğerlerini olduğu gibi bırak
            END
            WHERE tenant_id = ?
        `, [tenantId]);

        return;
    } catch (error) {
        console.error(error);
        throw error;
    } finally {
        conn.release();
    }
};

exports.disableMaliModeDB = async (tenantId) => {
    const conn = await getMySqlPromiseConnection();

    try {
        // Mali mode'u deaktifleştir
        await conn.query(`
            UPDATE store_details
            SET mali_mode_active = 0
            WHERE tenant_id = ?
        `, [tenantId]);

        // Payment types'ları eski haline döndür - Normal nakit göster, mali nakit gizle
        await conn.query(`
            UPDATE payment_types
            SET hide_on_pos = CASE
                WHEN isCash = 1 AND is_mali = 0 THEN 0  -- Normal nakit türlerini göster
                WHEN isCash = 1 AND is_mali = 1 THEN 1  -- Mali nakit türlerini gizle
                ELSE hide_on_pos  -- Diğerlerini olduğu gibi bırak
            END
            WHERE tenant_id = ?
        `, [tenantId]);

        return;
    } catch (error) {
        console.error(error);
        throw error;
    } finally {
        conn.release();
    }
};

exports.getMaliModeStatusDB = async (tenantId) => {
    const conn = await getMySqlPromiseConnection();

    try {
        const sql = `
            SELECT mali_mode_active
            FROM store_details
            WHERE tenant_id = ?
        `;

        const [result] = await conn.query(sql, [tenantId]);
        return result[0]?.mali_mode_active || 0;
    } catch (error) {
        console.error(error);
        throw error;
    } finally {
        conn.release();
    }
};
