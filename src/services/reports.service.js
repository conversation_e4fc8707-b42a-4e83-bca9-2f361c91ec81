const { getMySqlPromiseConnection } = require("../config/mysql.db")

exports.getOrdersCountDB = async (type, from, to, tenantId) => {
    const conn = await getMySqlPromiseConnection();
    try {

        const {filter, params} = await getFilterCondition('date', type, from, to, tenantId);

        const sql = `
        SELECT
            count(*) AS todays_orders
        FROM
            orders
        WHERE
            tenant_id = ? AND
            ${filter}
        `;

        const [result] = await conn.query(sql, [tenantId ,...params]);

        return result[0].todays_orders;
    } catch (error) {
        console.error(error);
        throw error;
    } finally {
        conn.release();
      }
};


exports.getNewCustomerCountDB = async (type, from, to, tenantId) => {
    const conn = await getMySqlPromiseConnection();
    try {

        const {filter, params} = await getFilterCondition('created_at', type, from, to, tenantId);

        const sql = `
        SELECT
            count(*) AS new_customers_count
        FROM
            customers
        WHERE
        tenant_id = ? AND
            ${filter}
        `;

        const [result] = await conn.query(sql, [ tenantId , ...params]);

        return result[0].new_customers_count;
    } catch (error) {
        console.error(error);
        throw error;
    } finally {
        conn.release();
      }
};

exports.getRepeatCustomerCountDB = async (type, from, to, tenantId) => {
    const conn = await getMySqlPromiseConnection();
    try {

        const {filter, params} = await getFilterCondition('date', type, from, to, tenantId);

        const sql = `
        SELECT
            COUNT(distinct customer_id) as todays_repeat_customers
        FROM
            orders
        WHERE
            tenant_id = ? AND
            ${filter}
            AND customer_type = 'CUSTOMER';
        `;

        const [result] = await conn.query(sql, [ tenantId, ...params]);

        return result[0].todays_repeat_customers;
    } catch (error) {
        console.error(error);
        throw error;
    } finally {
        conn.release();
      }
};

exports.getCancelledOrdersAndItemsDB = async (type, from, to, tenantId) => {
    const conn = await getMySqlPromiseConnection();
    try {
        const { filter, params } = await getFilterCondition('o.date', type, from, to, tenantId);

        // Toplam iptal tutarını hesapla
        const totalSql = `
        SELECT
            COALESCE(SUM(oi.price * oi.quantity), 0) as total_cancelled
        FROM
            order_items oi
            INNER JOIN orders o ON oi.order_id = o.id AND oi.tenant_id = o.tenant_id
        WHERE
            oi.status = 'cancelled' AND
            oi.tenant_id = ? AND
            ${filter}
        `;

        // İptal detaylarını getir
        const detailsSql = `
        SELECT
            oi.id as order_item_id,
            oi.order_id,
            o.date AS order_date,
            mi.title AS item_title,
            miv.title as variant_title,
            oi.price,
            oi.quantity,
            oi.price * oi.quantity AS total_price,
            o.delivery_type,
            o.customer_type,
            c.name AS customer_name,
            st.table_title,
            oi.cancelled_by as username,
            u.name as user_name,
            cr.title as reason_title,
            cr.id as reason_id
        FROM
            order_items oi
            INNER JOIN orders o ON oi.order_id = o.id AND oi.tenant_id = o.tenant_id
            INNER JOIN menu_items mi ON oi.item_id = mi.id AND oi.tenant_id = mi.tenant_id
            LEFT JOIN menu_item_variants miv ON oi.variant_id = miv.id AND oi.item_id = miv.item_id
            LEFT JOIN customers c ON o.customer_id = c.phone AND o.tenant_id = c.tenant_id
            LEFT JOIN store_tables st ON o.table_id = st.id
            LEFT JOIN users u ON oi.cancelled_by = u.username AND u.tenant_id = o.tenant_id
            LEFT JOIN cancellation_reasons cr ON oi.cancelled_reason_id = cr.id
        WHERE
            oi.status = 'cancelled' AND
            oi.tenant_id = ? AND
            ${filter}
        ORDER BY o.date DESC
        `;

        const [totalResult] = await conn.query(totalSql, [tenantId, ...params]);
        const [detailsResult] = await conn.query(detailsSql, [tenantId, ...params]);

        return {
            total: totalResult[0].total_cancelled,
            details: detailsResult
        };
    } catch (error) {
        console.error(error);
        throw error;
    } finally {
        conn.release();
    }
};



exports.getAverageOrderValueDB = async (type, from, to, tenantId) => {
    const conn = await getMySqlPromiseConnection();
    try {

        const {filter, params} = await getFilterCondition('created_at', type, from, to, tenantId);

        const sql = `
        SELECT
            avg(total) AS avg_order_value
        FROM
            invoices
        WHERE
            tenant_id = ? AND
            ${filter}
        `;

        const [result] = await conn.query(sql, [tenantId, ...params]);

        return result[0].avg_order_value;
    } catch (error) {
        console.error(error);
        throw error;
    } finally {
        conn.release();
    }
};

exports.getTotalPaymentsByPaymentTypesDB = async (type, from, to, tenantId) => {
    const conn = await getMySqlPromiseConnection();
    try {
        const { filter, params } = await getFilterCondition('created_at', type, from, to, tenantId);

        // payment_transactions tablosundan ödemeleri al (TEK KAYNAK!)
        const paymentTransactionSql = `
SELECT
    pt_trans.payment_type_id,
    COALESCE(pt.title, 'Ödeme Yöntemi Yok') AS title,
    CAST(SUM(pt_trans.amount) AS DECIMAL(10,2)) AS total
FROM
    payment_transactions pt_trans
LEFT JOIN
    payment_types pt
ON pt_trans.payment_type_id = pt.id AND pt_trans.tenant_id = pt.tenant_id
WHERE
    pt_trans.tenant_id = ? AND
    pt_trans.status = 'completed' AND
    pt_trans.transaction_type = 'payment' AND
    ${filter}
GROUP BY pt_trans.payment_type_id;

        `;

        const [paymentTransactionResults] = await conn.query(paymentTransactionSql, [tenantId, ...params]);

        // Sonuçları düzenle - Sadece payment_transactions (TEK KAYNAK!)
        const combinedResults = {};

        // payment_transactions sonuçlarını ekle (TEK KAYNAK!)
        paymentTransactionResults.forEach(result => {
            const paymentTypeId = result.payment_type_id || 'no_payment_method';
            if (!combinedResults[paymentTypeId]) {
                combinedResults[paymentTypeId] = {
                    payment_type_id: paymentTypeId,
                    title: result.title,
                    total: 0
                };
            }
            combinedResults[paymentTypeId].total += Number(result.total); // Sayıya çevirerek toplamaya zorla
        });

        // Object.values ile sonuçları diziye çevir
        return Object.values(combinedResults);


    } catch (error) {
        console.error(error);
        throw error;
    } finally {
        conn.release();
    }
};



exports.getTotalCustomersDB = async (tenantId) => {
    const conn = await getMySqlPromiseConnection();
    try {

        const sql = `
        SELECT
            count(*) AS total_customer
        FROM
            customers
        WHERE tenant_id = ?;
        `;

        const [result] = await conn.query(sql, [tenantId]);

        return result[0].total_customer;
    } catch (error) {
        console.error(error);
        throw error;
    } finally {
        conn.release();
      }
};

exports.getRevenueDB = async (type, from, to, tenantId) => {
    const conn = await getMySqlPromiseConnection();
    try {
        const { filter, params } = await getFilterCondition('o.date', type, from, to, tenantId);

        // Brüt ciro - tüm ödeme durumları dahil (pending, credit, paid, partial, refunded)
        // İptal edilen siparişler ve ürünler DAHİL
        // Zayi, ikram, indirimler dahil (bunlar ayrı raporlarda gösterilecek)
        const sql = `
        SELECT
            COALESCE(SUM(oi_total), 0) as total_revenue
        FROM (
            SELECT
                oi.order_id,
                SUM(oi.price * oi.quantity) as oi_total
            FROM
                orders o
                INNER JOIN order_items oi ON o.id = oi.order_id AND o.tenant_id = oi.tenant_id
            WHERE
                o.tenant_id = ? AND
                ${filter}
            GROUP BY oi.order_id
        ) aggregated_orders;
        `;

        const [result] = await conn.query(sql, [tenantId, ...params]);

        return result[0].total_revenue;
    } catch (error) {
        console.error(error);
        throw error;
    } finally {
        conn.release();
    }
};


exports.getCreditTotalDB = async (type, from, to, tenantId) => {
    const conn = await getMySqlPromiseConnection();
    try {
        const { filter, params } = await getFilterCondition('o.date', type, from, to, tenantId);

        // Toplam kredili satış tutarını hesapla
        const totalSql = `
        SELECT
            COALESCE(SUM(oi_total), 0) as total_credit
        FROM (
            SELECT
                oi.order_id,
                SUM(oi.price * oi.quantity) as oi_total
            FROM
                orders o
                INNER JOIN order_items oi ON o.id = oi.order_id AND o.tenant_id = oi.tenant_id
            WHERE
            payment_status IN ('credit') AND
                o.tenant_id = ? AND
                ${filter}
            GROUP BY oi.order_id
        ) aggregated_orders;
        `;

        // Kredili satış detaylarını getir
        const detailsSql = `
        SELECT
            o.id as order_id,
            o.date as order_date,
            o.customer_id,
            c.name as customer_name,
            c.phone as customer_phone,
            c.email as customer_email,
            c.address as customer_address,
            SUM(oi.price * oi.quantity) as order_total,
            o.username as created_by_username,
            u.name as created_by_name,
            st.table_title,
            o.delivery_type
        FROM
            orders o
            INNER JOIN order_items oi ON o.id = oi.order_id AND o.tenant_id = oi.tenant_id
            LEFT JOIN customers c ON o.customer_id = c.phone AND o.tenant_id = c.tenant_id
            LEFT JOIN users u ON o.username = u.username AND o.tenant_id = u.tenant_id
            LEFT JOIN store_tables st ON o.table_id = st.id
        WHERE
            o.payment_status = 'credit' AND
            o.tenant_id = ? AND
            ${filter}
        GROUP BY o.id
        ORDER BY o.date DESC
        `;

        const [totalResult] = await conn.query(totalSql, [tenantId, ...params]);
        const [detailsResult] = await conn.query(detailsSql, [tenantId, ...params]);

        return {
            total: totalResult[0].total_credit,
            details: detailsResult
        };
    } catch (error) {
        console.error(error);
        throw error;
    } finally {
        conn.release();
    }
};

exports.getTotalTaxDB = async (type, from, to, tenantId) => {
    const conn = await getMySqlPromiseConnection();
    try {

        const {filter, params} = await getFilterCondition('created_at', type, from, to, tenantId);

        const sql = `
        SELECT
            SUM(tax_total) AS total_tax
        FROM
            invoices
        WHERE
            tenant_id = ? AND
            ${filter}
        `;

        const [result] = await conn.query(sql, [tenantId,...params]);

        return result[0].total_tax;
    } catch (error) {
        console.error(error);
        throw error;
    } finally {
        conn.release();
      }
};


exports.getTotalNetRevenueDB = async (type, from, to, tenantId) => {
    const conn = await getMySqlPromiseConnection();
    try {
      const { filter, params } = await getFilterCondition('o.date', type, from, to, tenantId);

      // Net gelir: Sadece ödenen siparişler, iptal edilmemiş ürünler, indirimler düşülmüş
      const netRevenueSql = `
        SELECT
          COALESCE(SUM(order_total), 0) - COALESCE(SUM(order_discount), 0) AS total_net_revenue
        FROM (
          SELECT
            o.id,
            SUM(oi.price * oi.quantity) AS order_total,
            COALESCE(
              CASE
                WHEN od.discount_type = 'percentage' THEN
                  SUM(oi.price * oi.quantity) * (od.discount_value / 100)
                WHEN od.discount_type = 'amount' THEN od.discount_value
                ELSE 0
              END, 0
            ) AS order_discount
          FROM orders o
          INNER JOIN order_items oi ON o.id = oi.order_id AND o.tenant_id = oi.tenant_id
          LEFT JOIN order_discounts od ON o.id = od.order_id AND o.tenant_id = od.tenant_id
          WHERE o.payment_status = 'paid'
            AND o.status NOT IN ('cancelled')
            AND oi.status NOT IN ('cancelled', 'waste', 'complimentary')
            AND o.tenant_id = ?
            AND ${filter}
          GROUP BY o.id, od.discount_type, od.discount_value
        ) AS orders_with_discounts;
      `;

      const [netRevenueResult] = await conn.query(netRevenueSql, [tenantId, ...params]);

      return netRevenueResult[0]?.total_net_revenue || 0;
    } catch (error) {
      console.error(error);
      throw error;
    } finally {
      conn.release();
    }
  };

  exports.getTotalNetRevenueByFloorDB = async (type, from, to, tenantId) => {
    const conn = await getMySqlPromiseConnection();
    try {
      const { filter, params } = await getFilterCondition('o.date', type, from, to, tenantId);

      // Her alan için net gelir hesapla: Sadece ödenen siparişler, iptal edilmemiş ürünler, indirimler düşülmüş
      const netRevenueByFloorSql = `
        SELECT
          f.id as floor_id,
          f.title as floor_title,
          f.description as floor_description,
          COALESCE(SUM(order_total - order_discount), 0) AS net_revenue,
          COUNT(DISTINCT o.id) as order_count,
          COUNT(DISTINCT o.customer_id) as customer_count,
          COALESCE(AVG(order_total - order_discount), 0) as avg_order_value
        FROM floor f
        LEFT JOIN store_tables st ON f.id = st.floor AND f.tenant_id = st.tenant_id
        LEFT JOIN (
          SELECT
            o.id,
            o.table_id,
            o.customer_id,
            o.date,
            SUM(oi.price * oi.quantity) AS order_total,
            COALESCE(
              CASE
                WHEN od.discount_type = 'percentage' THEN
                  SUM(oi.price * oi.quantity) * (od.discount_value / 100)
                WHEN od.discount_type = 'amount' THEN od.discount_value
                ELSE 0
              END, 0
            ) AS order_discount
          FROM orders o
          INNER JOIN order_items oi ON o.id = oi.order_id AND o.tenant_id = oi.tenant_id
          LEFT JOIN order_discounts od ON o.id = od.order_id AND o.tenant_id = od.tenant_id
          WHERE o.payment_status = 'paid'
            AND o.status NOT IN ('cancelled')
            AND oi.status NOT IN ('cancelled', 'waste', 'complimentary')
            AND o.tenant_id = ?
            AND ${filter}
          GROUP BY o.id, o.table_id, o.customer_id, o.date, od.discount_type, od.discount_value
        ) o ON st.id = o.table_id
        WHERE f.tenant_id = ?
          AND f.is_active = 1
        GROUP BY f.id, f.title, f.description
        ORDER BY net_revenue DESC
      `;

      const [result] = await conn.query(netRevenueByFloorSql, [tenantId, ...params, tenantId]);

      return result;
    } catch (error) {
      console.error(error);
      throw error;
    } finally {
      conn.release();
    }
  };


exports.getTotalDiscountsDB = async (type, from, to, tenantId) => {
  const conn = await getMySqlPromiseConnection();
  try {
    const { filter, params } = await getFilterCondition('o.date', type, from, to, tenantId);

    // Toplam indirim tutarını hesapla
    const totalSql = `
      SELECT
        CAST(COALESCE(SUM(
          CASE
            WHEN od.discount_type = 'percentage' THEN
              (SELECT SUM(oi2.price * oi2.quantity)
               FROM order_items oi2
               WHERE oi2.order_id = od.order_id
               AND oi2.tenant_id = od.tenant_id
               AND oi2.status NOT IN ('cancelled')) * (od.discount_value / 100)
            WHEN od.discount_type = 'amount' THEN od.discount_value
            ELSE 0
          END
        ), 0) AS DECIMAL(10,2)) AS total_discounts
      FROM order_discounts od
      INNER JOIN orders o ON od.order_id = o.id AND od.tenant_id = o.tenant_id
      WHERE
        o.payment_status = 'paid'
        AND o.status NOT IN ('cancelled')
        AND o.tenant_id = ?
        AND ${filter}
    `;

    // İndirim detaylarını getir
    const detailsSql = `
      SELECT
        od.id as discount_id,
        od.order_id,
        o.date as order_date,
        od.order_item_id,
        mi.title as item_title,
        od.discount_type,
        CAST(od.discount_value AS DECIMAL(10,2)) AS discount_value,
        CAST(CASE
          WHEN od.discount_type = 'percentage' THEN
            CASE
              WHEN od.order_item_id IS NULL THEN (SUM(oi.price * oi.quantity) * (od.discount_value / 100))
              ELSE ((SELECT oi2.price * oi2.quantity FROM order_items oi2 WHERE oi2.id = od.order_item_id) * (od.discount_value / 100))
            END
          WHEN od.discount_type = 'amount' THEN od.discount_value
          ELSE 0
        END AS DECIMAL(10,2)) AS calculated_discount,
        od.created_by as username,
        u.name as user_name,
        od.created_at,
        st.table_title,
        o.delivery_type
      FROM
        order_discounts od
        INNER JOIN orders o ON od.order_id = o.id AND od.tenant_id = o.tenant_id
        LEFT JOIN order_items oi ON o.id = oi.order_id AND o.tenant_id = oi.tenant_id
        LEFT JOIN order_items oi_specific ON od.order_item_id = oi_specific.id
        LEFT JOIN menu_items mi ON oi_specific.item_id = mi.id
        LEFT JOIN users u ON od.created_by = u.username AND u.tenant_id = o.tenant_id
        LEFT JOIN store_tables st ON o.table_id = st.id
      WHERE
        o.payment_status = 'paid'
        AND o.status NOT IN ('cancelled')
        AND o.tenant_id = ? AND
        ${filter}
      GROUP BY od.id
      ORDER BY o.date DESC, od.id
    `;

    const [totalResult] = await conn.query(totalSql, [tenantId, ...params]);
    const [detailsResult] = await conn.query(detailsSql, [tenantId, ...params]);

    return {
      total: totalResult[0]?.total_discounts || 0,
      details: detailsResult
    };
  } catch (error) {
    console.error(error);
    throw error;
  } finally {
    conn.release();
  }
};


exports.getOpenOrdersDB = async (type, from, to, tenantId) => {
    const conn = await getMySqlPromiseConnection();
    try {
        const { filter, params } = await getFilterCondition('o.date', type, from, to, tenantId);

        // Ana sipariş bilgilerini getir
        const ordersSql = `
        SELECT
            o.id AS order_id,
            o.date AS order_date,
            o.delivery_type,
            o.table_id,
            st.table_title,
            c.name AS customer_name,
            o.payment_status,
            o.username AS created_by_username,
            u.name AS created_by_name
        FROM
            orders o
            LEFT JOIN store_tables st ON o.table_id = st.id
            LEFT JOIN customers c ON o.customer_id = c.phone AND o.tenant_id = c.tenant_id
            LEFT JOIN users u ON o.username = u.username AND o.tenant_id = u.tenant_id
        WHERE
            o.payment_status NOT IN ('paid', 'refunded', 'credit') AND
            o.status NOT IN ('cancelled') AND
            o.tenant_id = ? AND
            ${filter}
        ORDER BY o.date DESC;
        `;

        const [orders] = await conn.query(ordersSql, [tenantId, ...params]);

        // Sipariş öğelerini getir
        const orderIds = orders.map(order => order.order_id);

        if (orderIds.length === 0) {
            // Sipariş yoksa boş sonuç döndür
            return { dinein: [], takeaway: [], delivery: [], unknown: [] };
        }

        const orderItemsSql = `
        SELECT
            oi.order_id,
            oi.item_id,
            mi.title AS item_title,
            oi.variant_id,
            miv.title AS variant_title,
            oi.price,
            oi.quantity,
            oi.status,
            oi.notes,
            oi.addons
        FROM
            order_items oi
            LEFT JOIN menu_items mi ON oi.item_id = mi.id
            LEFT JOIN menu_item_variants miv ON oi.variant_id = miv.id AND oi.item_id = miv.item_id
        WHERE
            oi.order_id IN (?) AND
            oi.tenant_id = ?
        `;

        const [orderItems] = await conn.query(orderItemsSql, [orderIds, tenantId]);

        // Siparişlere öğeleri ekle ve toplam tutarı hesapla
        const ordersWithItems = orders.map(order => {
            const items = orderItems.filter(item => item.order_id === order.order_id);

            // Toplam tutarı hesapla
            let orderTotal = 0;
            items.forEach(item => {
                if (item.status !== 'cancelled' && item.status !== 'waste' && item.status !== 'complimentary') {
                    orderTotal += item.price * item.quantity;
                }
            });

            // Eklentileri JSON olarak parse et
            const itemsWithParsedAddons = items.map(item => {
                try {
                    if (item.addons) {
                        item.addons = JSON.parse(item.addons);
                    } else {
                        item.addons = [];
                    }
                } catch (e) {
                    item.addons = [];
                }
                return item;
            });

            return {
                ...order,
                items: itemsWithParsedAddons,
                order_total: orderTotal
            };
        });

        // Sipariş türlerine göre gruplandır
        const groupedOrders = ordersWithItems.reduce(
            (acc, order) => {
                const type = order.delivery_type || 'unknown';
                if (!acc[type]) acc[type] = [];
                acc[type].push(order);
                return acc;
            },
            { dinein: [], takeaway: [], delivery: [], unknown: [] }
        );

        return groupedOrders;
    } catch (error) {
        console.error(error);
        throw error;
    } finally {
        conn.release();
    }
};



exports.getTopSellingItemsDB = async (type, from, to, tenantId) => {
    const conn = await getMySqlPromiseConnection();
    try {
        const {filter, params} = await getFilterCondition('date', type, from, to, tenantId);

        const sql = `
        SELECT
            mi.*,
            oi_c.orders_count
        FROM
            menu_items mi
            INNER JOIN (
                SELECT
                    item_id,
                    SUM(quantity) AS orders_count
                FROM
                    order_items
                WHERE
                    tenant_id = ${tenantId} AND
                    status <> 'cancelled'
                    ${filter ? `AND ${filter}` : ''}
                GROUP BY
                    item_id
                ) oi_c ON mi.id = oi_c.item_id
        WHERE tenant_id = ${tenantId}
        ORDER BY
            oi_c.orders_count DESC;
        `;

        const [result] = await conn.query(sql, params);
        return result;
    } catch (error) {
        console.error(error);
        throw error;
    } finally {
        conn.release();
    }
};

const { getBusinessHoursDB } = require("./business-hours.service");

const getFilterCondition = async (field, type, from, to, tenantId) => {
    const params = [];
    let filter = '';

    // Business hours bilgisini al
    let businessHours = null;
    if (tenantId && (type === 'today' || type === 'yesterday')) {
        try {
            businessHours = await getBusinessHoursDB(tenantId);
        } catch (error) {
            console.error("Business hours alınamadı:", error);
        }
    }


    switch (type) {
        case 'custom': {
            if (from && to) {
                params.push(from, to);
                filter = `${field} >= ? AND ${field} <= ?`;
            }
            break;
        }
        case 'today': {
            if (businessHours) {
                const startTime = businessHours.day_start_time;
                const endTime = businessHours.day_end_time;
                const isOvernight = businessHours.is_overnight;

                if (isOvernight) {
                    // Gece yarısını geçen işletme için "bugün" mantığı
                    // Eğer şu anki saat kapanış saatinden küçükse, bu dünün iş gününün devamı
                    const now = new Date();
                    const currentHour = now.getHours();
                    const currentMinute = now.getMinutes();
                    const currentTime = currentHour * 60 + currentMinute; // dakika cinsinden

                    const [endHour, endMinute] = endTime.split(':').map(Number);
                    const businessEndTime = endHour * 60 + endMinute; // dakika cinsinden

                    if (currentTime <= businessEndTime) {
                        // Şu an kapanış saatinden önce - dünün iş gününün devamı
                        // Dün 16:00'dan bugün 04:00'a kadar
                        filter = `(
                            (${field} >= CONCAT(DATE_SUB(CURDATE(), INTERVAL 1 DAY), ' ${startTime}') AND ${field} <= CONCAT(DATE_SUB(CURDATE(), INTERVAL 1 DAY), ' 23:59:59'))
                            OR
                            (${field} >= CONCAT(CURDATE(), ' 00:00:00') AND ${field} <= CONCAT(CURDATE(), ' ${endTime}'))
                        )`;
                    } else {
                        // Şu an kapanış saatinden sonra - bugünün iş günü
                        // Bugün 16:00'dan yarın 04:00'a kadar
                        filter = `(
                            (${field} >= CONCAT(CURDATE(), ' ${startTime}') AND ${field} <= CONCAT(CURDATE(), ' 23:59:59'))
                            OR
                            (${field} >= CONCAT(DATE_ADD(CURDATE(), INTERVAL 1 DAY), ' 00:00:00') AND ${field} <= CONCAT(DATE_ADD(CURDATE(), INTERVAL 1 DAY), ' ${endTime}'))
                        )`;
                    }
                } else {
                    // Normal işletme (örn: 08:00-23:59)
                    filter = `${field} >= CONCAT(CURDATE(), ' ${startTime}') AND ${field} <= CONCAT(CURDATE(), ' ${endTime}')`;
                }

            } else {
                // Business hours yoksa normal filtreleme
                filter = `DATE(${field}) = CURDATE()`;

            }
            break;
        }
        case 'yesterday': {
            if (businessHours) {
                const startTime = businessHours.day_start_time;
                const endTime = businessHours.day_end_time;
                const isOvernight = businessHours.is_overnight;

                if (isOvernight) {
                    // Gece yarısını geçen işletme için "dün" mantığı
                    const now = new Date();
                    const currentHour = now.getHours();
                    const currentMinute = now.getMinutes();
                    const currentTime = currentHour * 60 + currentMinute; // dakika cinsinden

                    const [endHour, endMinute] = endTime.split(':').map(Number);
                    const businessEndTime = endHour * 60 + endMinute; // dakika cinsinden

                    if (currentTime <= businessEndTime) {
                        // Şu an kapanış saatinden önce - "dün" 2 gün öncesinden dün kapanışa kadar
                        // 2 gün önce 16:00'dan dün 04:00'a kadar
                        filter = `(
                            (${field} >= CONCAT(DATE_SUB(CURDATE(), INTERVAL 2 DAY), ' ${startTime}') AND ${field} <= CONCAT(DATE_SUB(CURDATE(), INTERVAL 2 DAY), ' 23:59:59'))
                            OR
                            (${field} >= CONCAT(DATE_SUB(CURDATE(), INTERVAL 1 DAY), ' 00:00:00') AND ${field} <= CONCAT(DATE_SUB(CURDATE(), INTERVAL 1 DAY), ' ${endTime}'))
                        )`;
                    } else {
                        // Şu an kapanış saatinden sonra - "dün" normal
                        // Dün 16:00'dan bugün 04:00'a kadar
                        filter = `(
                            (${field} >= CONCAT(DATE_SUB(CURDATE(), INTERVAL 1 DAY), ' ${startTime}') AND ${field} <= CONCAT(DATE_SUB(CURDATE(), INTERVAL 1 DAY), ' 23:59:59'))
                            OR
                            (${field} >= CONCAT(CURDATE(), ' 00:00:00') AND ${field} <= CONCAT(CURDATE(), ' ${endTime}'))
                        )`;
                    }
                } else {
                    // Normal işletme için dün
                    filter = `${field} >= CONCAT(DATE_SUB(CURDATE(), INTERVAL 1 DAY), ' ${startTime}') AND ${field} <= CONCAT(DATE_SUB(CURDATE(), INTERVAL 1 DAY), ' ${endTime}')`;
                }
            } else {
                // Business hours yoksa normal filtreleme
                filter = `DATE(${field}) = DATE_SUB(CURDATE(), INTERVAL 1 DAY)`;
            }
            break;
        }
        case 'tomorrow': {
            filter = `DATE(${field}) = DATE_ADD(CURDATE(), INTERVAL 1 DAY)`;
            break;
        }
        case 'this_month': {
            filter = `YEAR(${field}) = YEAR(NOW()) AND MONTH(${field}) = MONTH(NOW())`;
            break;
        }
        case 'last_month': {
            filter = `YEAR(${field}) = YEAR(DATE_SUB(NOW(), INTERVAL 1 MONTH)) AND MONTH(${field}) = MONTH(DATE_SUB(NOW(), INTERVAL 1 MONTH))`;
            break;
        }
        case 'last_7days': {
            filter = `DATE(${field}) >= DATE_SUB(CURDATE(), INTERVAL 7 DAY) AND DATE(${field}) <= CURDATE()`;
            break;
        }
        default: {
            filter = '';
        }
    }

    return { params, filter };
};

exports.getWasteTotalDB = async (type, from, to, tenantId) => {
  const conn = await getMySqlPromiseConnection();
  try {
    const { filter, params } = await getFilterCondition('o.date', type, from, to, tenantId);

    // Toplam zayi tutarını hesapla
    const totalSql = `
    SELECT
      CAST(COALESCE(SUM(oi.price * oi.quantity), 0) AS DECIMAL(10,2)) as total_waste
    FROM
      orders o
      INNER JOIN order_items oi ON o.id = oi.order_id AND o.tenant_id = oi.tenant_id
    WHERE
      oi.status = 'waste' AND
      o.tenant_id = ? AND
      ${filter}
    `;

    // Zayi detaylarını getir
    const detailsSql = `
    SELECT
      oi.id as order_item_id,
      o.id as order_id,
      o.date as order_date,
      mi.title as item_title,
      miv.title as variant_title,
      CAST(oi.price AS DECIMAL(10,2)) AS price,
      oi.quantity,
      CAST(oi.price * oi.quantity AS DECIMAL(10,2)) as total_price,
      oi.waste_by as username,
      u.name as user_name,
      wr.title as reason_title,
      st.table_title,
      o.delivery_type
    FROM
      orders o
      INNER JOIN order_items oi ON o.id = oi.order_id AND o.tenant_id = oi.tenant_id
      LEFT JOIN menu_items mi ON oi.item_id = mi.id
      LEFT JOIN menu_item_variants miv ON oi.variant_id = miv.id AND oi.item_id = miv.item_id
      LEFT JOIN users u ON oi.waste_by = u.username AND u.tenant_id = o.tenant_id
      LEFT JOIN waste_reasons wr ON oi.waste_reason_id = wr.id
      LEFT JOIN store_tables st ON o.table_id = st.id
    WHERE
      oi.status = 'waste' AND
      o.tenant_id = ? AND
      ${filter}
    ORDER BY o.date DESC
    `;

    const [totalResult] = await conn.query(totalSql, [tenantId, ...params]);
    const [detailsResult] = await conn.query(detailsSql, [tenantId, ...params]);

    return {
      total: totalResult[0].total_waste,
      details: detailsResult
    };
  } catch (error) {
    console.error(error);
    throw error;
  } finally {
    conn.release();
  }
};

exports.getComplimentaryTotalDB = async (type, from, to, tenantId) => {
  const conn = await getMySqlPromiseConnection();
  try {
    const { filter, params } = await getFilterCondition('o.date', type, from, to, tenantId);

    // Toplam ikram tutarını hesapla
    const totalSql = `
    SELECT
      CAST(COALESCE(SUM(oi.price * oi.quantity), 0) AS DECIMAL(10,2)) as total_complimentary
    FROM
      orders o
      INNER JOIN order_items oi ON o.id = oi.order_id AND o.tenant_id = oi.tenant_id
    WHERE
      oi.status = 'complimentary' AND
      o.tenant_id = ? AND
      ${filter}
    `;

    // İkram detaylarını getir
    const detailsSql = `
    SELECT
      oi.id as order_item_id,
      o.id as order_id,
      o.date as order_date,
      mi.title as item_title,
      miv.title as variant_title,
      CAST(oi.price AS DECIMAL(10,2)) AS price,
      oi.quantity,
      CAST(oi.price * oi.quantity AS DECIMAL(10,2)) as total_price,
      oi.complimentary_by as username,
      u.name as user_name,
      cr.title as reason_title,
      st.table_title,
      o.delivery_type
    FROM
      orders o
      INNER JOIN order_items oi ON o.id = oi.order_id AND o.tenant_id = oi.tenant_id
      LEFT JOIN menu_items mi ON oi.item_id = mi.id
      LEFT JOIN menu_item_variants miv ON oi.variant_id = miv.id AND oi.item_id = miv.item_id
      LEFT JOIN users u ON oi.complimentary_by = u.username AND u.tenant_id = o.tenant_id
      LEFT JOIN complimentary_reasons cr ON oi.complimentary_reason_id = cr.id
      LEFT JOIN store_tables st ON o.table_id = st.id
    WHERE
      oi.status = 'complimentary' AND
      o.tenant_id = ? AND
      ${filter}
    ORDER BY o.date DESC
    `;

    const [totalResult] = await conn.query(totalSql, [tenantId, ...params]);
    const [detailsResult] = await conn.query(detailsSql, [tenantId, ...params]);

    return {
      total: totalResult[0].total_complimentary,
      details: detailsResult
    };
  } catch (error) {
    console.error(error);
    throw error;
  } finally {
    conn.release();
  }
};

// Garson Satış Raporu
exports.getWaiterSalesReportDB = async (type, from, to, tenantId) => {
    const conn = await getMySqlPromiseConnection();

    try {
        const { filter, params } = await getFilterCondition('o.date', type, from, to, tenantId);

        // Garsonların toplam satış tutarlarını hesapla
        const waiterTotalsSql = `
            SELECT
                o.username as waiter_username,
                u.name as waiter_name,
                COUNT(DISTINCT o.id) as total_orders,
                CAST(COALESCE(SUM(oi.price * oi.quantity), 0) AS DECIMAL(10,2)) as total_sales,
                COUNT(oi.id) as total_items_sold
            FROM
                orders o
                JOIN order_items oi ON o.id = oi.order_id AND o.tenant_id = oi.tenant_id
                LEFT JOIN users u ON o.username = u.username AND u.tenant_id = o.tenant_id
            WHERE
                o.payment_status = 'paid'
                AND o.status NOT IN ('cancelled')
                AND oi.status NOT IN ('cancelled', 'waste', 'complimentary')
                AND o.tenant_id = ?
                AND ${filter}
            GROUP BY
                o.username, u.name
            ORDER BY
                total_sales DESC
        `;

        const [waiterTotals] = await conn.query(waiterTotalsSql, [tenantId, ...params]);

        // Her garson için detaylı ürün satışlarını getir
        const waiterDetails = [];

        for (const waiter of waiterTotals) {
            const detailsSql = `
                SELECT
                    mi.title as item_title,
                    miv.title as variant_title,
                    CAST(oi.price AS DECIMAL(10,2)) as price,
                    SUM(oi.quantity) as total_quantity,
                    CAST(SUM(oi.price * oi.quantity) AS DECIMAL(10,2)) as total_amount,
                    COUNT(DISTINCT o.id) as order_count
                FROM
                    orders o
                    JOIN order_items oi ON o.id = oi.order_id AND o.tenant_id = oi.tenant_id
                    LEFT JOIN menu_items mi ON oi.item_id = mi.id
                    LEFT JOIN menu_item_variants miv ON oi.variant_id = miv.id AND oi.item_id = miv.item_id
                WHERE
                    o.payment_status = 'paid'
                    AND o.status NOT IN ('cancelled')
                    AND oi.status NOT IN ('cancelled', 'waste', 'complimentary')
                    AND o.username = ?
                    AND o.tenant_id = ?
                    AND ${filter}
                GROUP BY
                    oi.item_id, oi.variant_id, mi.title, miv.title, oi.price
                ORDER BY
                    total_amount DESC
            `;

            const [itemDetails] = await conn.query(detailsSql, [waiter.waiter_username, tenantId, ...params]);

            waiterDetails.push({
                ...waiter,
                item_details: itemDetails
            });
        }

        return waiterDetails;
    } catch (error) {
        console.error(error);
        throw error;
    } finally {
        conn.release();
    }
};
