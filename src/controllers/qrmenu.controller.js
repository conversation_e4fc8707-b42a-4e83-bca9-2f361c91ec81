const { getAllMenuItemsDB, getAllAddonsDB, getAllVariantsDB } = require("../services/menu_item.service");
const { getStoreSettingDB, getCategoriesDB, getTenantIdFromQRCode, getStoreTableByEncryptedIdDB , placeOrderViaQrMenuDB} = require("../services/settings.service");
const { getRecommendationsForTenant, generateQuestionsAndRecommendations, getFilteredRecommendations  } = require("../services/recommendations.service");
const {
  addFeedBacksDB,
  deleteFeedBacksDB,
  listCampaignsDB,
} = require("../services/qr_menu.service");

const{ getTranslationsByTenant } = require("../services/translations.service");

exports.addFeedback = async (req, res) => {
  try {
    const qrcode = req.params.qrcode;
    
    const tenantId = await getTenantIdFromQRCode(qrcode);
    if (!tenantId) {
      return res.status(404).json({ message: "<PERSON><PERSON> bulunamadı!" });
    }

    const { feedback_type, message } = req.body;

    // Gerekli alan kontrolü
    if (!feedback_type || !["thankyou", "suggestion", "complaint"].includes(feedback_type)) {
      return res.status(200).json({ message: "Geçersiz geri bildirim türü!" });
    }
    if (!message) {
      return res.status(200).json({ message: "Mesaj alanı boş olamaz!" });
    }

    const feedbackId = await addFeedBacksDB(feedback_type, message, tenantId);

    return res.status(200).json({
      success: true,
      message: "Geri bildirim başarıyla kaydedildi!",
      feedbackId,
    });
  } catch (error) {
    console.error("Geri bildirim kaydedilirken hata:", error);
    return res.status(500).json({ message: "Bir hata oluştu." });
  }
};

// Yeni: Feedback silme
exports.deleteFeedback = async (req, res) => {
  try {
    const qrcode = req.params.qrcode;

    const tenantId = await getTenantIdFromQRCode(qrcode);
    if (!tenantId) {
      return res.status(404).json({ message: "Menü bulunamadı!" });
    }

    const { feedbackId } = req.params;

    await deleteFeedBacksDB(feedbackId, tenantId);

    return res.status(200).json({
      success: true,
      message: "Geri bildirim başarıyla silindi!",
    });
  } catch (error) {
    console.error("Geri bildirim silinirken hata:", error);
    return res.status(500).json({ message: "Bir hata oluştu." });
  }
};

// İlk önerileri döndüren endpoint
exports.getDynamicRecommendations = async (req, res) => {
  try {
    const qrcode = req.params.qrcode;

    const tenantId = await getTenantIdFromQRCode(qrcode);
    if (!tenantId) {
      return res.status(404).json({ message: "Menü bulunamadı!" });
    }

    const menuItems = await getAllMenuItemsDB(tenantId);

    // İlk önerileri oluştur
    const { questions, recommendations } =
      await generateQuestionsAndRecommendations(menuItems);

    return res.status(200).json({
      success: true,
      questions,
      recommendations,
    });
  } catch (error) {
    console.error("Dinamik öneriler oluşturulurken hata:", error);
    return res.status(500).json({ message: "Bir hata oluştu." });
  }
};

// Müşteri yanıtlarına göre öneriler döndüren endpoint
exports.getRecommendationsForUser = async (req, res) => {
  try {
    const qrcode = req.params.qrcode;
    const userAnswers = req.body.answers; // Yanıtlar POST isteğiyle alınır

    const tenantId = await getTenantIdFromQRCode(qrcode);
    if (!tenantId) {
      return res.status(404).json({ message: "Menü bulunamadı!" });
    }

    const menuItems = await getAllMenuItemsDB(tenantId);

    const recommendations = await getFilteredRecommendations(menuItems, userAnswers);

    return res.status(200).json({
      success: true,
      recommendations,
    });
  } catch (error) {
    console.error("Yanıtlara dayalı öneriler alınırken hata:", error);
    return res.status(500).json({ message: "Bir hata oluştu." });
  }
};

exports.getQRMenuInit = async (req, res) => {
  try {
    const qrcode = req.params.qrcode;
    const tableId = req.query.tableId;

    const tenantId = await getTenantIdFromQRCode(qrcode);

    if (!tenantId) {
      return res.status(404).json({
        success: false,
        message: "QR/Digital Menu not found!",
      });
    }

    const currentDate = new Date().toISOString().slice(0, 19).replace('T', ' '); // Şu anki tarih (YYYY-MM-DD HH:MM:SS)

    const [categories, translations, storeSettings, storeTable, menuItems, addons, variants, campaigns] = await Promise.all([
      getCategoriesDB(tenantId),
      getTranslationsByTenant(tenantId),
      getStoreSettingDB(tenantId),
      getStoreTableByEncryptedIdDB(tenantId, tableId),
      getAllMenuItemsDB(tenantId),
      getAllAddonsDB(tenantId),
      getAllVariantsDB(tenantId),
      listCampaignsDB(tenantId, currentDate), // Aktif kampanyaları getir
    ]);

    const formattedMenuItems = menuItems.map((item) => {
      const itemAddons = addons.filter((addon) => addon.item_id == item.id);
      const itemVariants = variants.filter((variant) => variant.item_id == item.id);

      return {
        ...item,
        addons: [...itemAddons],
        variants: [...itemVariants],
      };
    });


    return res.status(200).json({
      categories: categories,
      storeSettings: storeSettings,
      menuItems: formattedMenuItems,
      storeTable: storeTable,
      translations: translations,
      campaigns: campaigns, // Kampanyaları ekle
    });
  } catch (error) {
    console.error(error);
    return res.status(500).json({
      success: false,
      message: "Birşeyler ters gitti! Lütfen daha sonra tekrar deneyin.",
    });
  }
};

exports.placeOrderViaQrMenu = async (req, res) => {

    try {
      const qrcode = req.params.qrcode;

      const tenantId = await getTenantIdFromQRCode(qrcode);

      const {deliveryType , cartItems, customerType, customer, tableId} = req.body;

      console.log(tableId)

      if(cartItems?.length == 0) {
        return res.status(400).json({
          success: false,
          message: "Sepet Boş!"
        });
      }

      const result = await placeOrderViaQrMenuDB(tenantId, deliveryType , cartItems, customerType, customer.phone || null, tableId || null , customer.name || null);

      return res.status(200).json({
        success: true,
        message: `Order Placed Successfully`,
        orderId: result.orderId,
      });

    } catch (error) {
      console.error(error);
      return res.status(500).json({
        success: false,
        message: "Error processing the request, please try after sometime!"
      });
    }

  };

  exports.getRecommendations = async (req, res) => {
    try {
        const qrcode = req.params.qrcode;

        const tenantId = await getTenantIdFromQRCode(qrcode);

        if (!tenantId) {
            return res.status(404).json({
                success: false,
                message: "QR/Digital Menu not found!"
            });
        }

        const recommendations = await getRecommendationsForTenant(tenantId);

        return res.status(200).json({
            success: true,
            recommendations
        });
    } catch (error) {
        console.error(error);
        return res.status(500).json({
            success: false,
            message: "Birşeyler ters gitti! Lütfen daha sonra tekrar deneyin."
        });
    }
};

